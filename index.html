<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple 3D Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: Arial, sans-serif;
        }

        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
        }

        /* Inventory overlay */
        #inventoryOverlay {
            position: absolute;
            inset: 0;
            display: none;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.55);
            z-index: 200;
        }
        #inventoryOverlay.open {
            display: flex;
        }
        .inventory {
            width: 560px;
            max-width: calc(100vw - 40px);
            padding: 16px;
            background: rgba(18, 18, 18, 0.92);
            border: 1px solid #555;
            border-radius: 10px;
            color: #fff;
            box-shadow: 0 10px 30px rgba(0,0,0,0.45);
        }
        .inventory-title {
            font-weight: 600;
            margin-bottom: 10px;
            opacity: 0.9;
        }
        .inventory-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 10px;
        }
        .slot {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.07);
            border: 1px solid #666;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        .slot.drag-over {
            outline: 2px dashed #9ecfff;
            outline-offset: -4px;
        }
        .item {
            width: 72px;
            height: 72px;
            border: 1px solid #999;
            border-radius: 8px;
            background: rgba(255,255,255,0.14);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: grab;
            user-select: none;
            position: relative;
        }
        .item:active { cursor: grabbing; }
        .item.selected { outline: 2px solid #ffd54f; }
        .item .emoji { font-size: 26px; }
        .item .label {
            position: absolute;
            bottom: 4px;
            left: 4px;
            right: 4px;
            font-size: 11px;
            text-align: center;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div id="info">
        <div>WASD to move, Mouse to look, Space to jump, I to open inventory</div>
        <div>Position: <span id="position">0, 0, 0</span></div>
        <div>Health: <span id="health">100</span></div>
    </div>

    <div id="gameContainer"></div>

    <!-- Inventory Overlay -->
    <div id="inventoryOverlay" aria-hidden="true">
        <div class="inventory" role="dialog" aria-modal="true" aria-label="Inventory">
            <div class="inventory-title">Inventory (press I to close)</div>
            <div id="inventoryGrid" class="inventory-grid"></div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="game.js?v=3"></script>
</body>
</html>
