<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lightindar 3D - Prototype</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #87CEEB;
            font-family: Arial, sans-serif;
        }
        
        #gameContainer {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        #ui {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            z-index: 100;
            background: rgba(0,0,0,0.5);
            padding: 10px;
            border-radius: 5px;
        }
        
        #instructions {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: white;
            z-index: 100;
            background: rgba(0,0,0,0.5);
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
        }
        
        canvas {
            display: block;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="ui">
            <h3>Lightindar 3D - Phase 1 Prototype</h3>
            <div>Position: <span id="position">0, 0, 0</span></div>
            <div>Health: <span id="health">100</span></div>
        </div>
        
        <div id="instructions">
            <strong>Controls:</strong><br>
            WASD - Move<br>
            Mouse - Look around<br>
            Space - Jump<br>
            Click to lock mouse cursor
        </div>
    </div>

    <!-- Three.js CDN - Updated version -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r150/three.min.js">
    <script src="game.js"></script>
</body>
</html>
