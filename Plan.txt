Lightindar 3D — Development Plan (LID) 
Game Type

    Genre: 3D open-world adventure

    Perspective: Third-person, free-moving camera (like Tears of the Kingdom)

    Player Control: WASD for movement, mouse for camera rotation, space to jump

    Focus: Exploration, story-driven quests,  combat,  


World Setup 
a blank worl for now 

Player Mechanics

    Movement:

        WASD walking/running

        Jumping & simple gravity

        Camera orbit, zoom, and potential shoulder-switch

    Combat:

        Basic melee attacks with swords and other stuff

        Special abilities tied to the Water element (water waves, bubble shield)

   

Development Roadmap

Phase 1 — Prototype (what we just started)

    Third-person character controller (done in basic form).

    Flat world with movement & camera.

    Simple placeholder character and environment.

Phase 2 — Exploration Base

    Terrain sculpting & basic landmarks.

    Collision & boundaries.

    NPC placeholders.

Phase 3 — Combat

    Basic attack animations.

    Enemy AI that moves toward and attacks the player.

    Health and damage system.

Phase 4 — Story & Quests

    Dialogue boxes.

    Quest triggers.

    Crystal collection mechanic.

Phase 5 — World Building

    Populate zones with props, foliage, structures.

    Add unique elemental areas.

Phase 6 — Polish

    Replace placeholders with real models.

    Add sound, effects, and menu systems.

