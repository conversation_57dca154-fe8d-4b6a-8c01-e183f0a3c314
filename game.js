// Lightindar 3D - Phase 1 Prototype
// Basic 3D open world game with third-person controls

class Game {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.player = null;
        this.ground = null;
        
        // Player state
        this.playerPosition = new THREE.Vector3(0, 1, 0);
        this.playerVelocity = new THREE.Vector3(0, 0, 0);
        this.isGrounded = false;
        this.health = 100;
        
        // Controls
        this.keys = {};
        this.mouse = { x: 0, y: 0 };
        this.cameraAngle = { horizontal: 0, vertical: 0 };
        this.isPointerLocked = false;
        
        // Game settings
        this.moveSpeed = 5;
        this.jumpForce = 8;
        this.gravity = -20;
        this.cameraDistance = 8;
        this.cameraHeight = 3;
        
        // Inventory state
        this.isInventoryOpen = false;
        this.inventorySlots = new Array(12).fill(null);
        this.itemsById = new Map();
        this.selectedItemIds = new Set();
        this.equippedItemIds = new Set();
        this.equipmentMeshes = new Map();
        this.swordSwingState = {
            isSwinging: false,
            startTime: 0,
            duration: 0.6,
            comboStep: 0, // 0 = thrust, 1 = left swing, 2 = right swing
            lastAttackTime: 0,
            comboWindow: 2000 // 2 seconds to continue combo
        };
        this.shieldBlockState = { isBlocking: false };
        
        this.init();
    }
    
    init() {
        try {
            console.log('Initializing game...');
            this.setupScene();
            console.log('Scene setup complete');
            this.createWorld();
            console.log('World created');
            this.createPlayer();
            console.log('Player created');
            this.setupUI();
            console.log('UI setup');
            this.setupControls();
            console.log('Controls setup');
            this.animate();
            console.log('Animation started');
        } catch (error) {
            console.error('Error initializing game:', error);
        }
    }
    
    setupScene() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // Sky blue

        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );

        // Set initial camera position
        this.camera.position.set(0, 5, 10);
        this.camera.lookAt(0, 0, 0);
        
        // Create renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        const container = document.getElementById('gameContainer');
        if (container) {
            container.appendChild(this.renderer.domElement);
            console.log('Renderer added to DOM');
        } else {
            console.error('Game container not found!');
        }
        
        // Add lighting
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);
    }
    
    createWorld() {
        // Create flat ground plane
        const groundGeometry = new THREE.PlaneGeometry(200, 200);
        const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x4a7c59 }); // Green grass
        this.ground = new THREE.Mesh(groundGeometry, groundMaterial);
        this.ground.rotation.x = -Math.PI / 2;
        this.ground.receiveShadow = true;
        this.scene.add(this.ground);
        console.log('Ground added to scene');

        // Add some basic landmarks (cubes for now)
        this.createLandmarks();
        console.log('Scene children count:', this.scene.children.length);
    }
    
    createLandmarks() {
        const cubeGeometry = new THREE.BoxGeometry(2, 2, 2);
        const materials = [
            new THREE.MeshLambertMaterial({ color: 0xff6b6b }), // Red
            new THREE.MeshLambertMaterial({ color: 0x4ecdc4 }), // Teal
            new THREE.MeshLambertMaterial({ color: 0x45b7d1 }), // Blue
            new THREE.MeshLambertMaterial({ color: 0xf9ca24 }), // Yellow
        ];
        
        const positions = [
            { x: 10, z: 10 },
            { x: -15, z: 8 },
            { x: 20, z: -12 },
            { x: -8, z: -20 }
        ];
        
        positions.forEach((pos, index) => {
            const cube = new THREE.Mesh(cubeGeometry, materials[index]);
            cube.position.set(pos.x, 1, pos.z);
            cube.castShadow = true;
            this.scene.add(cube);
        });
    }
    
    createPlayer() {
        // Simple player representation (box for now, more compatible)
        const playerGeometry = new THREE.BoxGeometry(1, 2, 1);
        const playerMaterial = new THREE.MeshLambertMaterial({ color: 0x6c5ce7 }); // Purple
        this.player = new THREE.Mesh(playerGeometry, playerMaterial);
        this.player.position.copy(this.playerPosition);
        this.player.castShadow = true;
        this.scene.add(this.player);

        // Attachment anchors for equipment (approximate hand/side positions)
        this.rightHandAnchor = new THREE.Object3D();
        this.rightHandAnchor.position.set(0.5, 0.9, -0.1);
        this.player.add(this.rightHandAnchor);

        this.leftHandAnchor = new THREE.Object3D();
        this.leftHandAnchor.position.set(-0.5, 0.9, 0.1);
        this.player.add(this.leftHandAnchor);
    }
    
    setupControls() {
        // Keyboard controls
        document.addEventListener('keydown', (event) => {
            this.keys[event.code] = true;
            
            // Jump
            if (event.code === 'Space' && this.isGrounded) {
                this.playerVelocity.y = this.jumpForce;
                this.isGrounded = false;
                event.preventDefault();
            }
            
            // Toggle inventory
            if (event.code === 'KeyI' || (event.key && event.key.toLowerCase() === 'i')) {
                console.log('Toggle Inventory key detected');
                this.toggleInventory();
                event.preventDefault();
            }
            // Close with Escape
            if (event.code === 'Escape' && this.isInventoryOpen) {
                this.toggleInventory(false);
                event.preventDefault();
            }
        });
        
        document.addEventListener('keyup', (event) => {
            this.keys[event.code] = false;
        });
        
        // Mouse controls
        document.addEventListener('click', () => {
            if (!this.isInventoryOpen) {
                this.renderer.domElement.requestPointerLock();
            }
        });
        
        // Left click for sword swing
        document.addEventListener('mousedown', (event) => {
            if (event.button === 0 && !this.isInventoryOpen && this.equippedItemIds.has('sword')) {
                this.startSwordSwing();
            }
        });
        
        // Right click for shield block
        document.addEventListener('mousedown', (event) => {
            if (event.button === 2 && !this.isInventoryOpen && this.equippedItemIds.has('shield')) {
                this.toggleShieldBlock();
            }
        });
        
        // Prevent context menu on right click
        document.addEventListener('contextmenu', (event) => {
            event.preventDefault();
        });
        
        document.addEventListener('pointerlockchange', () => {
            this.isPointerLocked = document.pointerLockElement === this.renderer.domElement;
        });
        
        document.addEventListener('mousemove', (event) => {
            if (this.isPointerLocked) {
                this.cameraAngle.horizontal -= event.movementX * 0.002;
                this.cameraAngle.vertical -= event.movementY * 0.002;
                this.cameraAngle.vertical = Math.max(-Math.PI/3, Math.min(Math.PI/3, this.cameraAngle.vertical));
            }
        });
        
        // Handle window resize
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
    }
    
    setupUI() {
        this.positionElement = document.getElementById('position');
        this.inventoryOverlay = document.getElementById('inventoryOverlay');
        this.inventoryGrid = document.getElementById('inventoryGrid');
        this.setupInventory();
    }
    
    setupInventory() {
        // Initialize items
        const initialItems = [
            { id: 'sword', name: 'Sword', emoji: '🗡️' },
            { id: 'shield', name: 'Shield', emoji: '🛡️' },
        ];
        initialItems.forEach((item) => this.itemsById.set(item.id, item));
        // Place initial items
        this.inventorySlots[0] = 'sword';
        this.inventorySlots[1] = 'shield';
        
        // Render grid and items
        this.renderInventory();
        
        // Close overlay when clicking outside the inventory panel
        if (this.inventoryOverlay) {
            this.inventoryOverlay.addEventListener('click', (e) => {
                if (e.target === this.inventoryOverlay) {
                    this.toggleInventory(false);
                }
            });
        }
    }
    
    renderInventory() {
        if (!this.inventoryGrid) return;
        // Clear grid
        this.inventoryGrid.innerHTML = '';
        
        for (let slotIndex = 0; slotIndex < this.inventorySlots.length; slotIndex++) {
            const slotElement = document.createElement('div');
            slotElement.className = 'slot';
            slotElement.dataset.index = String(slotIndex);
            
            // Drag target handlers
            slotElement.addEventListener('dragover', (e) => {
                e.preventDefault();
                slotElement.classList.add('drag-over');
            });
            slotElement.addEventListener('dragleave', () => {
                slotElement.classList.remove('drag-over');
            });
            slotElement.addEventListener('drop', (e) => {
                e.preventDefault();
                slotElement.classList.remove('drag-over');
                const draggedItemId = e.dataTransfer?.getData('text/plain');
                if (!draggedItemId) return;
                const toIndex = Number(slotElement.dataset.index);
                this.moveItemToSlot(draggedItemId, toIndex);
            });
            
            const itemId = this.inventorySlots[slotIndex];
            if (itemId) {
                const item = this.itemsById.get(itemId);
                if (item) {
                    const itemEl = document.createElement('div');
                    itemEl.className = 'item' + (this.selectedItemIds.has(item.id) ? ' selected' : '');
                    itemEl.setAttribute('draggable', 'true');
                    itemEl.dataset.itemId = item.id;
                    itemEl.innerHTML = `
                        <div class="emoji">${item.emoji}</div>
                        <div class="label">${item.name}</div>
                    `;
                    
                    // Click to select
                    itemEl.addEventListener('click', (e) => {
                        e.stopPropagation();
                        // Toggle selection/equip
                        if (this.selectedItemIds.has(item.id)) {
                            this.selectedItemIds.delete(item.id);
                            this.unequipItem(item.id);
                        } else {
                            this.selectedItemIds.add(item.id);
                            this.equipItem(item.id);
                        }
                        this.renderInventory();
                    });
                    
                    // Drag handlers
                    itemEl.addEventListener('dragstart', (e) => {
                        e.dataTransfer?.setData('text/plain', item.id);
                        e.dataTransfer?.setDragImage(itemEl, 36, 36);
                    });
                    
                    slotElement.appendChild(itemEl);
                }
            }
            
            this.inventoryGrid.appendChild(slotElement);
        }
    }
    
    moveItemToSlot(itemId, toIndex) {
        if (toIndex < 0 || toIndex >= this.inventorySlots.length) return;
        const fromIndex = this.inventorySlots.indexOf(itemId);
        if (fromIndex === -1 && !this.itemsById.has(itemId)) return;
        
        const targetItem = this.inventorySlots[toIndex];
        // Place dragged item into target slot
        this.inventorySlots[toIndex] = itemId;
        // If it came from a different slot, move/swap
        if (fromIndex !== -1 && fromIndex !== toIndex) {
            this.inventorySlots[fromIndex] = targetItem || null;
        }
        this.renderInventory();
    }
    
    toggleInventory(forceState) {
        const nextState = typeof forceState === 'boolean' ? forceState : !this.isInventoryOpen;
        this.isInventoryOpen = nextState;
        if (this.inventoryOverlay) {
            if (this.isInventoryOpen) {
                this.inventoryOverlay.classList.add('open');
                this.inventoryOverlay.setAttribute('aria-hidden', 'false');
                if (document.pointerLockElement) {
                    document.exitPointerLock();
                }
            } else {
                this.inventoryOverlay.classList.remove('open');
                this.inventoryOverlay.setAttribute('aria-hidden', 'true');
            }
        }
    }
    
    equipItem(itemId) {
        if (!itemId || !this.itemsById.has(itemId)) {
            return;
        }
        if (this.equippedItemIds.has(itemId)) {
            return;
        }
        const mesh = this.createEquipmentMesh(itemId);
        if (mesh) {
            mesh.castShadow = true;
            this.equipmentMeshes.set(itemId, mesh);
            this.equippedItemIds.add(itemId);
            // Attach to appropriate anchor with a nicer pose
            if (itemId === 'sword' && this.rightHandAnchor) {
                this.rightHandAnchor.add(mesh);
                // Right side, same height as shield, blade pointing forward, slight tilt to align guard with side
                mesh.position.set(0.06, -0.70, -0.02);
                // Point blade forward (-Z), slight yaw/roll to line guard with block side and tuck closer; add slight downward tip tilt
                mesh.rotation.set(-Math.PI / 2 - 0.1, 0.25, Math.PI + 0.05);
            } else if (itemId === 'shield' && this.leftHandAnchor) {
                this.leftHandAnchor.add(mesh);
                // Sit on the left side, facing outward slightly forward
                mesh.position.set(-0.10, -0.70, 0.02);
                // Flip to face outward and cant slightly forward
                mesh.rotation.set(0.0, Math.PI, -Math.PI / 2);
                mesh.rotateY(-0.15);
            } else {
                // Fallback attach to player
                this.player.add(mesh);
            }
        }
    }
    
    unequipItem(itemId) {
        if (!this.equippedItemIds.has(itemId)) return;
        const mesh = this.equipmentMeshes.get(itemId);
        if (mesh && mesh.parent) {
            mesh.parent.remove(mesh);
        }
        this.equipmentMeshes.delete(itemId);
        this.equippedItemIds.delete(itemId);
    }
    
    createEquipmentMesh(itemId) {
        if (itemId === 'sword') return this.createDetailedSword();
        if (itemId === 'shield') return this.createDetailedShield();
        return null;
    }
    
    createDetailedSword() {
        const group = new THREE.Group();
        // Materials
        const metal = new THREE.MeshStandardMaterial({ color: 0xcfd8dc, metalness: 0.8, roughness: 0.3 });
        const darkMetal = new THREE.MeshStandardMaterial({ color: 0x9ea7ad, metalness: 0.9, roughness: 0.25 });
        const leather = new THREE.MeshStandardMaterial({ color: 0x5d4037, metalness: 0.0, roughness: 0.9 });
        
        // Blade
        const bladeHeight = 1.2;
        const blade = new THREE.Mesh(new THREE.BoxGeometry(0.12, bladeHeight, 0.06), metal);
        blade.position.y = bladeHeight / 2;
        blade.castShadow = true;
        group.add(blade);
        // Tip (pyramid-ish using cone with 4 segments)
        const tip = new THREE.Mesh(new THREE.ConeGeometry(0.085, 0.22, 4), metal);
        tip.position.y = bladeHeight + 0.11;
        tip.rotation.y = Math.PI / 4; // align faces with blade edges
        tip.castShadow = true;
        group.add(tip);
        // Fuller (groove)
        const fuller = new THREE.Mesh(new THREE.BoxGeometry(0.035, bladeHeight * 0.85, 0.01), darkMetal);
        fuller.position.y = bladeHeight * 0.5;
        fuller.position.z = 0.018;
        group.add(fuller);
        
        // Guard
        const guard = new THREE.Mesh(new THREE.BoxGeometry(0.38, 0.08, 0.18), darkMetal);
        guard.position.y = 0.04;
        guard.castShadow = true;
        group.add(guard);
        
        // Grip
        const gripLen = 0.34;
        const grip = new THREE.Mesh(new THREE.CylinderGeometry(0.055, 0.055, gripLen, 16), leather);
        grip.position.y = -gripLen / 2 - 0.04;
        grip.castShadow = true;
        group.add(grip);
        
        // Pommel
        const pommel = new THREE.Mesh(new THREE.SphereGeometry(0.085, 16, 16), darkMetal);
        pommel.position.y = -gripLen - 0.08;
        pommel.castShadow = true;
        group.add(pommel);
        
        return group;
    }
    
    createDetailedShield() {
        const group = new THREE.Group();
        const wood = new THREE.MeshStandardMaterial({ color: 0x8d6e63, metalness: 0.0, roughness: 1.0 });
        const metal = new THREE.MeshStandardMaterial({ color: 0xb0bec5, metalness: 0.85, roughness: 0.35 });
        const leather = new THREE.MeshStandardMaterial({ color: 0x6d4c41, metalness: 0.0, roughness: 0.9 });
        
        // Main disc
        const disc = new THREE.Mesh(new THREE.CylinderGeometry(0.56, 0.56, 0.08, 32), wood);
        disc.castShadow = true;
        disc.receiveShadow = true;
        group.add(disc);
        
        // Metal rim
        const rim = new THREE.Mesh(new THREE.TorusGeometry(0.56, 0.04, 12, 32), metal);
        rim.rotation.x = Math.PI / 2;
        rim.position.z = 0.0;
        rim.castShadow = true;
        group.add(rim);
        
        // Boss (front hemisphere)
        const boss = new THREE.Mesh(new THREE.SphereGeometry(0.18, 20, 16, 0, Math.PI * 2, 0, Math.PI / 2), metal);
        boss.position.z = 0.06;
        boss.castShadow = true;
        group.add(boss);
        
        // Straps (on back)
        const strap1 = new THREE.Mesh(new THREE.BoxGeometry(0.45, 0.06, 0.02), leather);
        strap1.position.set(-0.12, 0.0, -0.045);
        const strap2 = new THREE.Mesh(new THREE.BoxGeometry(0.25, 0.06, 0.02), leather);
        strap2.position.set(0.12, -0.12, -0.045);
        group.add(strap1);
        group.add(strap2);
        
        return group;
    }
    
    update(deltaTime) {
        this.handleMovement(deltaTime);
        this.updatePhysics(deltaTime);
        this.updateCamera();
        this.updateSwordSwing(deltaTime);
        this.updateShieldBlock();
        this.updateUI();
    }
    
    handleMovement(deltaTime) {
        if (this.isInventoryOpen) {
            // Disable movement while inventory is open
            this.playerVelocity.x *= 0.8;
            this.playerVelocity.z *= 0.8;
            return;
        }
        const moveVector = new THREE.Vector3();
        
        // Calculate movement direction based on camera angle
        if (this.keys['KeyW']) moveVector.z -= 1;
        if (this.keys['KeyS']) moveVector.z += 1;
        if (this.keys['KeyA']) moveVector.x -= 1;
        if (this.keys['KeyD']) moveVector.x += 1;
        
        if (moveVector.length() > 0) {
            moveVector.normalize();
            
            // Rotate movement vector based on camera horizontal angle
            const rotatedMove = moveVector.clone();
            rotatedMove.applyAxisAngle(new THREE.Vector3(0, 1, 0), this.cameraAngle.horizontal);
            
            // Apply movement
            this.playerVelocity.x = rotatedMove.x * this.moveSpeed;
            this.playerVelocity.z = rotatedMove.z * this.moveSpeed;
            
            // Rotate player to face movement direction when moving
            if (this.player) {
                const targetRotation = Math.atan2(rotatedMove.x, rotatedMove.z);
                this.player.rotation.y = targetRotation;
            }
        } else {
            // Apply friction when not moving
            this.playerVelocity.x *= 0.8;
            this.playerVelocity.z *= 0.8;
        }
    }
    
    startSwordSwing() {
        if (this.swordSwingState.isSwinging) return;

        const now = performance.now();
        const timeSinceLastAttack = now - this.swordSwingState.lastAttackTime;

        // Check if we're within combo window
        if (timeSinceLastAttack <= this.swordSwingState.comboWindow) {
            // Continue combo
            this.swordSwingState.comboStep = (this.swordSwingState.comboStep + 1) % 3;
        } else {
            // Reset to first attack
            this.swordSwingState.comboStep = 0;
        }

        this.swordSwingState.isSwinging = true;
        this.swordSwingState.startTime = now;
        this.swordSwingState.lastAttackTime = now;
    }
    
    updateSwordSwing(deltaTime) {
        if (!this.swordSwingState.isSwinging) return;
        
        const elapsed = (performance.now() - this.swordSwingState.startTime) / 1000;
        const progress = elapsed / this.swordSwingState.duration;
        
        if (progress >= 1.0) {
            // Swing complete, reset
            this.swordSwingState.isSwinging = false;
            const swordMesh = this.equipmentMeshes.get('sword');
            if (swordMesh) {
                // Reset to original position and rotation
                swordMesh.position.set(0.06, -0.70, -0.02);
                swordMesh.rotation.set(-Math.PI / 2 - 0.1, 0.25, Math.PI + 0.05);
            }
            return;
        }
        
        const swordMesh = this.equipmentMeshes.get('sword');
        if (!swordMesh) return;

        // Different animations based on combo step
        if (this.swordSwingState.comboStep === 0) {
            // First attack: Thrust (bigger thrust - full blade extends)
            let thrustOffset;
            if (progress < 0.5) {
                thrustOffset = progress * 2 * 1.5; // Bigger thrust
            } else {
                thrustOffset = (1 - (progress - 0.5) * 2) * 1.5;
            }
            swordMesh.position.set(0.06, -0.70, -0.02 + thrustOffset);

        } else if (this.swordSwingState.comboStep === 1) {
            // Second attack: Left to right swing
            let swingAngle;
            if (progress < 0.5) {
                // Swing from left tilt to center
                swingAngle = -Math.PI/4 + (progress * 2) * (Math.PI/4);
            } else {
                // Continue to right side
                swingAngle = ((progress - 0.5) * 2) * (Math.PI/4);
            }
            swordMesh.rotation.set(-Math.PI / 2 - 0.1, 0.25 + swingAngle, Math.PI + 0.05);

        } else if (this.swordSwingState.comboStep === 2) {
            // Third attack: Right to left swing (reverse)
            let swingAngle;
            if (progress < 0.5) {
                // Swing from right to center
                swingAngle = Math.PI/4 - (progress * 2) * (Math.PI/4);
            } else {
                // Continue to left side
                swingAngle = -((progress - 0.5) * 2) * (Math.PI/4);
            }
            swordMesh.rotation.set(-Math.PI / 2 - 0.1, 0.25 + swingAngle, Math.PI + 0.05);
        }
    }
    
    toggleShieldBlock() {
        if (!this.equippedItemIds.has('shield')) return;
        this.shieldBlockState.isBlocking = !this.shieldBlockState.isBlocking;
        this.updateShieldBlock();
    }
    
    updateShieldBlock() {
        const shieldMesh = this.equipmentMeshes.get('shield');
        if (!shieldMesh) return;

        if (this.shieldBlockState.isBlocking) {
            // Move shield to blocking position - level with sword, closer to block
            shieldMesh.position.set(0.0, -0.7, 0.4);
            // Tilt horizontally and face forward for blocking - boss facing outward
            shieldMesh.rotation.set(Math.PI / 2, 0.0, 0.0);
        } else {
            // Return to side position
            shieldMesh.position.set(-0.10, -0.70, 0.02);
            // Face outward with slight forward cant
            shieldMesh.rotation.set(0.0, Math.PI, -Math.PI / 2);
            shieldMesh.rotateY(-0.15);
        }
    }
    
    updatePhysics(deltaTime) {
        // Apply gravity
        if (!this.isGrounded) {
            this.playerVelocity.y += this.gravity * deltaTime;
        }
        
        // Update position
        this.playerPosition.add(this.playerVelocity.clone().multiplyScalar(deltaTime));
        
        // Ground collision
        if (this.playerPosition.y <= 1) {
            this.playerPosition.y = 1;
            this.playerVelocity.y = 0;
            this.isGrounded = true;
        }
        
        // Update player mesh position
        this.player.position.copy(this.playerPosition);
    }
    
    updateCamera() {
        // Third-person camera positioning
        const cameraOffset = new THREE.Vector3(
            Math.sin(this.cameraAngle.horizontal) * this.cameraDistance,
            this.cameraHeight + Math.sin(this.cameraAngle.vertical) * 3,
            Math.cos(this.cameraAngle.horizontal) * this.cameraDistance
        );

        this.camera.position.copy(this.playerPosition).add(cameraOffset);
        this.camera.lookAt(this.playerPosition);

        // Debug camera position
        if (Math.random() < 0.01) { // Log occasionally to avoid spam
            console.log('Camera pos:', this.camera.position, 'Player pos:', this.playerPosition);
        }
    }
    
    updateUI() {
        const pos = this.playerPosition;
        this.positionElement.textContent = `${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}, ${pos.z.toFixed(1)}`;
        // Health is now displayed as hearts, no need to update text
    }
    
    animate() {
        const clock = new THREE.Clock();
        
        const gameLoop = () => {
            const deltaTime = clock.getDelta();
            this.update(deltaTime);
            this.renderer.render(this.scene, this.camera);
            requestAnimationFrame(gameLoop);
        };
        
        gameLoop();
    }
}

// Start the game when page loads
window.addEventListener('load', () => {
    new Game();
});
