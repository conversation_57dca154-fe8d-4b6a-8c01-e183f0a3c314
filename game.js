// Lightindar 3D - Phase 1 Prototype
// Basic 3D open world game with third-person controls

class Game {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.player = null;
        this.ground = null;
        
        // Player state
        this.playerPosition = new THREE.Vector3(0, 1, 0);
        this.playerVelocity = new THREE.Vector3(0, 0, 0);
        this.isGrounded = false;
        this.health = 100;
        
        // Controls
        this.keys = {};
        this.mouse = { x: 0, y: 0 };
        this.cameraAngle = { horizontal: 0, vertical: 0 };
        this.isPointerLocked = false;
        
        // Game settings
        this.moveSpeed = 5;
        this.jumpForce = 8;
        this.gravity = -20;
        this.cameraDistance = 8;
        this.cameraHeight = 3;
        
        this.init();
    }
    
    init() {
        this.setupScene();
        this.createWorld();
        this.createPlayer();
        this.setupControls();
        this.setupUI();
        this.animate();
    }
    
    setupScene() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // Sky blue
        
        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        
        // Create renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        document.getElementById('gameContainer').appendChild(this.renderer.domElement);
        
        // Add lighting
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);
    }
    
    createWorld() {
        // Create flat ground plane
        const groundGeometry = new THREE.PlaneGeometry(200, 200);
        const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x4a7c59 }); // Green grass
        this.ground = new THREE.Mesh(groundGeometry, groundMaterial);
        this.ground.rotation.x = -Math.PI / 2;
        this.ground.receiveShadow = true;
        this.scene.add(this.ground);
        
        // Add some basic landmarks (cubes for now)
        this.createLandmarks();
    }
    
    createLandmarks() {
        const cubeGeometry = new THREE.BoxGeometry(2, 2, 2);
        const materials = [
            new THREE.MeshLambertMaterial({ color: 0xff6b6b }), // Red
            new THREE.MeshLambertMaterial({ color: 0x4ecdc4 }), // Teal
            new THREE.MeshLambertMaterial({ color: 0x45b7d1 }), // Blue
            new THREE.MeshLambertMaterial({ color: 0xf9ca24 }), // Yellow
        ];
        
        const positions = [
            { x: 10, z: 10 },
            { x: -15, z: 8 },
            { x: 20, z: -12 },
            { x: -8, z: -20 }
        ];
        
        positions.forEach((pos, index) => {
            const cube = new THREE.Mesh(cubeGeometry, materials[index]);
            cube.position.set(pos.x, 1, pos.z);
            cube.castShadow = true;
            this.scene.add(cube);
        });
    }
    
    createPlayer() {
        // Simple player representation (capsule-like shape)
        const playerGeometry = new THREE.CapsuleGeometry(0.5, 1.5, 4, 8);
        const playerMaterial = new THREE.MeshLambertMaterial({ color: 0x6c5ce7 }); // Purple
        this.player = new THREE.Mesh(playerGeometry, playerMaterial);
        this.player.position.copy(this.playerPosition);
        this.player.castShadow = true;
        this.scene.add(this.player);
    }
    
    setupControls() {
        // Keyboard controls
        document.addEventListener('keydown', (event) => {
            this.keys[event.code] = true;
            
            // Jump
            if (event.code === 'Space' && this.isGrounded) {
                this.playerVelocity.y = this.jumpForce;
                this.isGrounded = false;
                event.preventDefault();
            }
        });
        
        document.addEventListener('keyup', (event) => {
            this.keys[event.code] = false;
        });
        
        // Mouse controls
        document.addEventListener('click', () => {
            this.renderer.domElement.requestPointerLock();
        });
        
        document.addEventListener('pointerlockchange', () => {
            this.isPointerLocked = document.pointerLockElement === this.renderer.domElement;
        });
        
        document.addEventListener('mousemove', (event) => {
            if (this.isPointerLocked) {
                this.cameraAngle.horizontal -= event.movementX * 0.002;
                this.cameraAngle.vertical -= event.movementY * 0.002;
                this.cameraAngle.vertical = Math.max(-Math.PI/3, Math.min(Math.PI/3, this.cameraAngle.vertical));
            }
        });
        
        // Handle window resize
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
    }
    
    setupUI() {
        this.positionElement = document.getElementById('position');
        this.healthElement = document.getElementById('health');
    }
    
    update(deltaTime) {
        this.handleMovement(deltaTime);
        this.updatePhysics(deltaTime);
        this.updateCamera();
        this.updateUI();
    }
    
    handleMovement(deltaTime) {
        const moveVector = new THREE.Vector3();
        
        // Calculate movement direction based on camera angle
        if (this.keys['KeyW']) moveVector.z -= 1;
        if (this.keys['KeyS']) moveVector.z += 1;
        if (this.keys['KeyA']) moveVector.x -= 1;
        if (this.keys['KeyD']) moveVector.x += 1;
        
        if (moveVector.length() > 0) {
            moveVector.normalize();
            
            // Rotate movement vector based on camera horizontal angle
            const rotatedMove = moveVector.clone();
            rotatedMove.applyAxisAngle(new THREE.Vector3(0, 1, 0), this.cameraAngle.horizontal);
            
            // Apply movement
            this.playerVelocity.x = rotatedMove.x * this.moveSpeed;
            this.playerVelocity.z = rotatedMove.z * this.moveSpeed;
        } else {
            // Apply friction when not moving
            this.playerVelocity.x *= 0.8;
            this.playerVelocity.z *= 0.8;
        }
    }
    
    updatePhysics(deltaTime) {
        // Apply gravity
        if (!this.isGrounded) {
            this.playerVelocity.y += this.gravity * deltaTime;
        }
        
        // Update position
        this.playerPosition.add(this.playerVelocity.clone().multiplyScalar(deltaTime));
        
        // Ground collision
        if (this.playerPosition.y <= 1) {
            this.playerPosition.y = 1;
            this.playerVelocity.y = 0;
            this.isGrounded = true;
        }
        
        // Update player mesh position
        this.player.position.copy(this.playerPosition);
    }
    
    updateCamera() {
        // Third-person camera positioning
        const cameraOffset = new THREE.Vector3(
            Math.sin(this.cameraAngle.horizontal) * this.cameraDistance,
            this.cameraHeight + Math.sin(this.cameraAngle.vertical) * 3,
            Math.cos(this.cameraAngle.horizontal) * this.cameraDistance
        );
        
        this.camera.position.copy(this.playerPosition).add(cameraOffset);
        this.camera.lookAt(this.playerPosition);
    }
    
    updateUI() {
        const pos = this.playerPosition;
        this.positionElement.textContent = `${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}, ${pos.z.toFixed(1)}`;
        this.healthElement.textContent = this.health;
    }
    
    animate() {
        const clock = new THREE.Clock();
        
        const gameLoop = () => {
            const deltaTime = clock.getDelta();
            this.update(deltaTime);
            this.renderer.render(this.scene, this.camera);
            requestAnimationFrame(gameLoop);
        };
        
        gameLoop();
    }
}

// Start the game when page loads
window.addEventListener('load', () => {
    new Game();
});
